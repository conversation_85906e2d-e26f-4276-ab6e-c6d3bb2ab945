#!/usr/bin/env python3
"""
Test du nouveau système de rôles métier BTP
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def test_company_roles():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    print('🧪 Test du système de rôles métier BTP...')
    
    try:
        # 1. Vérifier les enums disponibles
        print('\n📋 Enums disponibles:')
        
        # Enum userrole (rôles système)
        user_roles = await conn.fetch("SELECT unnest(enum_range(NULL::userrole)) as role_value")
        print('   userrole (rôles système):')
        for role in user_roles:
            print(f'     - {role["role_value"]}')
        
        # Enum companyrole (rôles métier BTP)
        company_roles = await conn.fetch("SELECT unnest(enum_range(NULL::companyrole)) as role_value")
        print('   companyrole (rôles métier BTP):')
        for role in company_roles:
            print(f'     - {role["role_value"]}')
        
        # 2. Vérifier les données actuelles
        print('\n📊 Données actuelles:')
        
        # Table users
        users = await conn.fetch('SELECT email, role FROM users')
        print('   Table users (rôles système):')
        for user in users:
            print(f'     - {user["email"]}: {user["role"]}')
        
        # Table user_companies
        user_companies = await conn.fetch('''
            SELECT u.email, uc.role as company_role, c.name as company_name
            FROM user_companies uc
            JOIN users u ON uc.user_id = u.id
            JOIN companies c ON uc.company_id = c.id
        ''')
        print('   Table user_companies (rôles métier BTP):')
        for uc in user_companies:
            print(f'     - {uc["email"]} dans {uc["company_name"]}: {uc["company_role"]}')
        
        # 3. Test d'insertion d'un nouveau rôle métier
        print('\n🧪 Test d\'insertion d\'un rôle métier...')
        
        # Récupérer un utilisateur et une entreprise existants
        user_data = await conn.fetchrow('SELECT id, email FROM users LIMIT 1')
        company_data = await conn.fetchrow('SELECT id, name FROM companies LIMIT 1')
        
        if user_data and company_data:
            # Tester l'insertion d'un nouveau rôle métier
            try:
                await conn.execute('''
                    INSERT INTO user_companies (user_id, company_id, role)
                    VALUES ($1, $2, $3::companyrole)
                    ON CONFLICT (user_id, company_id) DO UPDATE SET role = EXCLUDED.role
                ''', user_data['id'], company_data['id'], 'archi')
                
                print(f'   ✅ Rôle "archi" assigné à {user_data["email"]} dans {company_data["name"]}')
                
                # Vérifier l'insertion
                result = await conn.fetchrow('''
                    SELECT role FROM user_companies 
                    WHERE user_id = $1 AND company_id = $2
                ''', user_data['id'], company_data['id'])
                
                print(f'   ✅ Vérification: rôle = {result["role"]}')
                
            except Exception as e:
                print(f'   ❌ Erreur insertion: {e}')
        
        # 4. Tester les valeurs invalides
        print('\n🧪 Test de valeur invalide...')
        try:
            await conn.execute('''
                INSERT INTO user_companies (user_id, company_id, role)
                VALUES ($1, $2, $3::companyrole)
            ''', 999, 999, 'invalid_role')
            print('   ❌ Erreur: valeur invalide acceptée!')
        except Exception as e:
            print(f'   ✅ Valeur invalide rejetée: {str(e)[:100]}...')
        
        await conn.close()
        print('\n🎉 Test terminé avec succès!')
        return True
        
    except Exception as e:
        print(f'\n❌ Erreur: {e}')
        await conn.close()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_company_roles())
    if success:
        print('\n✅ Le système de rôles métier BTP fonctionne correctement!')
    else:
        print('\n❌ Problème avec le système de rôles')
