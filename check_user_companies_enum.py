#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_user_companies():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    # Vérifier la structure de user_companies
    columns = await conn.fetch("""
        SELECT column_name, data_type, udt_name 
        FROM information_schema.columns 
        WHERE table_name = 'user_companies' 
        AND column_name = 'role'
    """)
    
    print('🔍 Structure colonne role dans user_companies:')
    for col in columns:
        print(f'  - {col["column_name"]}: {col["data_type"]} ({col["udt_name"]})')
    
    # Vérifier les valeurs actuelles
    roles = await conn.fetch('SELECT DISTINCT role FROM user_companies')
    print('\n🔍 Rôles actuels dans user_companies:')
    for role in roles:
        print(f'  - {role["role"]}')
    
    # Vérifier s'il y a un enum pour user_companies
    try:
        enum_info = await conn.fetch("""
            SELECT t.typname, e.enumlabel 
            FROM pg_type t 
            JOIN pg_enum e ON t.oid = e.enumtypid 
            WHERE t.typname LIKE '%role%' OR t.typname LIKE '%company%'
            ORDER BY t.typname, e.enumsortorder
        """)
        
        print('\n🔍 Enums liés aux rôles:')
        current_type = None
        for enum in enum_info:
            if enum['typname'] != current_type:
                current_type = enum['typname']
                print(f'\n  {current_type}:')
            print(f'    - {enum["enumlabel"]}')
            
    except Exception as e:
        print(f'\n❌ Erreur enum: {e}')
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_user_companies())
