#!/usr/bin/env python3
"""
Script pour corriger les erreurs RLS (Row Level Security) dans Supabase
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def fix_rls_security():
    """Corriger les erreurs RLS en activant la sécurité sur toutes les tables"""
    
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Correction des erreurs RLS (Row Level Security)")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # Liste COMPLÈTE des tables à sécuriser
        tables = [
            # Tables principales existantes
            'companies', 'users', 'user_companies', 'projects', 'materials',
            'material_categories', 'budget_lines', 'time_entries', 'documents',

            # Nouvelles tables détectées
            'company_settings', 'employees', 'suppliers', 'financial_reports',
            'quote_templates', 'project_documents', 'project_employees',
            'employee_assignments', 'supplier_contacts', 'budgets', 'invoices',
            'purchase_orders', 'quotes', 'technical_sheets', 'price_history',
            'document_versions', 'document_folders', 'payments', 'user_profiles',
            'purchase_order_lines', 'deliveries', 'quote_lines', 'delivery_lines',
            'company_invitations', 'audit_logs'
        ]
        
        print(f"\n🔒 Activation de RLS sur {len(tables)} tables...")
        
        # 1. Activer RLS sur toutes les tables
        for table in tables:
            try:
                await conn.execute(f"ALTER TABLE public.{table} ENABLE ROW LEVEL SECURITY;")
                print(f"  ✅ RLS activé sur {table}")
            except Exception as e:
                if "already enabled" in str(e).lower():
                    print(f"  ⚠️ RLS déjà activé sur {table}")
                else:
                    print(f"  ❌ Erreur sur {table}: {e}")
        
        print(f"\n🛡️ Création des politiques de sécurité...")
        
        # 2. Créer des politiques de base pour les super admins
        # Politique générique pour les super admins sur toutes les tables principales
        main_tables = ['companies', 'users', 'projects', 'employees', 'suppliers',
                      'budgets', 'invoices', 'quotes', 'purchase_orders', 'deliveries',
                      'financial_reports', 'audit_logs', 'company_settings']

        policies = []

        # Créer une politique super admin pour chaque table principale
        for table in main_tables:
            policies.append(f"""
            CREATE POLICY "Super admins have full access" ON public.{table}
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.users
                        WHERE users.supabase_user_id::text = auth.uid()::text
                        AND users.role = 'super_admin'
                    )
                );
            """)

        # Politiques spéciales pour les tables de référence (lecture globale)
        reference_tables = ['materials', 'material_categories', 'quote_templates',
                           'technical_sheets', 'document_folders']

        for table in reference_tables:
            policies.append(f"""
            CREATE POLICY "Authenticated users can read {table}" ON public.{table}
                FOR SELECT USING (auth.uid() IS NOT NULL);
            """)

        # Politique pour les profils utilisateurs (accès à son propre profil)
        policies.append("""
        CREATE POLICY "Users can access their own profile" ON public.user_profiles
            FOR ALL USING (
                user_id IN (
                    SELECT id FROM public.users
                    WHERE users.supabase_user_id::text = auth.uid()::text
                )
            );
        """)
        
        for i, policy in enumerate(policies, 1):
            try:
                await conn.execute(policy)
                print(f"  ✅ Politique {i} créée")
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"  ⚠️ Politique {i} existe déjà")
                else:
                    print(f"  ❌ Erreur politique {i}: {e}")
        
        # 3. Vérifier l'état RLS
        print(f"\n📊 Vérification de l'état RLS...")
        
        # Vérifier TOUTES les tables publiques
        result = await conn.fetch("""
            SELECT
                schemaname,
                tablename,
                rowsecurity as rls_enabled
            FROM pg_tables
            WHERE schemaname = 'public'
            ORDER BY tablename;
        """)
        
        print(f"\n📋 État RLS de toutes les tables publiques ({len(result)} tables):")

        enabled_count = 0
        disabled_count = 0

        for row in result:
            if row['rls_enabled']:
                enabled_count += 1
                status = "✅ Activé"
            else:
                disabled_count += 1
                status = "❌ Désactivé"
            print(f"  - {row['tablename']}: {status}")

        print(f"\n📊 Résumé: {enabled_count} tables sécurisées, {disabled_count} tables non sécurisées")
        
        await conn.close()
        print("\n🎉 Correction RLS terminée avec succès!")
        print("\n📝 Note: Notre API FastAPI utilise une connexion directe PostgreSQL")
        print("   donc RLS n'affectera pas le fonctionnement de l'application.")
        print("   RLS sécurise uniquement les accès via PostgREST/Supabase Client.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_rls_security())
    if success:
        print("\n✅ Les erreurs RLS Supabase ont été corrigées.")
        print("   Vérifiez dans l'interface Supabase que les erreurs ont disparu.")
    else:
        print("\n❌ Correction RLS échouée.")
