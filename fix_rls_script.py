#!/usr/bin/env python3
"""
Script pour corriger les erreurs RLS (Row Level Security) dans Supabase
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def fix_rls_security():
    """Corriger les erreurs RLS en activant la sécurité sur toutes les tables"""
    
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Correction des erreurs RLS (Row Level Security)")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # Liste des tables à sécuriser
        tables = [
            'companies', 'users', 'user_companies', 'projects', 'tasks',
            'materials', 'material_categories', 'budget_lines', 
            'time_entries', 'documents', 'notifications'
        ]
        
        print(f"\n🔒 Activation de RLS sur {len(tables)} tables...")
        
        # 1. Activer RLS sur toutes les tables
        for table in tables:
            try:
                await conn.execute(f"ALTER TABLE public.{table} ENABLE ROW LEVEL SECURITY;")
                print(f"  ✅ RLS activé sur {table}")
            except Exception as e:
                if "already enabled" in str(e).lower():
                    print(f"  ⚠️ RLS déjà activé sur {table}")
                else:
                    print(f"  ❌ Erreur sur {table}: {e}")
        
        print(f"\n🛡️ Création des politiques de sécurité...")
        
        # 2. Créer des politiques de base pour les super admins
        policies = [
            # Super admins ont accès total
            """
            CREATE POLICY "Super admins have full access" ON public.companies
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.users 
                        WHERE users.supabase_user_id::text = auth.uid()::text 
                        AND users.role = 'super_admin'
                    )
                );
            """,
            """
            CREATE POLICY "Super admins have full access" ON public.users
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.users 
                        WHERE users.supabase_user_id::text = auth.uid()::text 
                        AND users.role = 'super_admin'
                    )
                );
            """,
            """
            CREATE POLICY "Super admins have full access" ON public.projects
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.users 
                        WHERE users.supabase_user_id::text = auth.uid()::text 
                        AND users.role = 'super_admin'
                    )
                );
            """,
            # Politique de lecture pour les matériaux (accès global)
            """
            CREATE POLICY "Authenticated users can read materials" ON public.materials
                FOR SELECT USING (auth.uid() IS NOT NULL);
            """,
            """
            CREATE POLICY "Authenticated users can read material categories" ON public.material_categories
                FOR SELECT USING (auth.uid() IS NOT NULL);
            """
        ]
        
        for i, policy in enumerate(policies, 1):
            try:
                await conn.execute(policy)
                print(f"  ✅ Politique {i} créée")
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"  ⚠️ Politique {i} existe déjà")
                else:
                    print(f"  ❌ Erreur politique {i}: {e}")
        
        # 3. Vérifier l'état RLS
        print(f"\n📊 Vérification de l'état RLS...")
        
        result = await conn.fetch("""
            SELECT 
                schemaname,
                tablename,
                rowsecurity as rls_enabled
            FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN ('companies', 'users', 'projects', 'materials', 'material_categories', 'budget_lines')
            ORDER BY tablename;
        """)
        
        print("\n📋 État RLS des tables principales:")
        for row in result:
            status = "✅ Activé" if row['rls_enabled'] else "❌ Désactivé"
            print(f"  - {row['tablename']}: {status}")
        
        await conn.close()
        print("\n🎉 Correction RLS terminée avec succès!")
        print("\n📝 Note: Notre API FastAPI utilise une connexion directe PostgreSQL")
        print("   donc RLS n'affectera pas le fonctionnement de l'application.")
        print("   RLS sécurise uniquement les accès via PostgREST/Supabase Client.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_rls_security())
    if success:
        print("\n✅ Les erreurs RLS Supabase ont été corrigées.")
        print("   Vérifiez dans l'interface Supabase que les erreurs ont disparu.")
    else:
        print("\n❌ Correction RLS échouée.")
