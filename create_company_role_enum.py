#!/usr/bin/env python3
"""
Script pour créer l'enum des rôles métier BTP dans user_companies
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def create_company_role_enum():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    print('🔧 Création de l\'enum companyrole pour les rôles métier BTP...')
    
    try:
        # 1. Créer l'enum companyrole
        print('   📝 Création de l\'enum companyrole...')
        await conn.execute("""
            DO $$ BEGIN
                CREATE TYPE companyrole AS ENUM (
                    'moa',      -- Maître d'Ouvrage
                    'moadel',   -- Maître d'Ouvrage Délégué  
                    'archi',    -- Architecte
                    'be',       -- Bureau d'Études
                    'bc',       -- Bureau de Contrôle
                    'opc',      -- Ordonnancement Pilotage Coordination
                    'ent',      -- Entreprise
                    'fo'        -- Fournisseur
                );
            EXCEPTION
                WHEN duplicate_object THEN null;
            END $$;
        """)
        print('   ✅ Enum companyrole créé')
        
        # 2. Vérifier les valeurs actuelles dans user_companies
        current_roles = await conn.fetch('SELECT DISTINCT role FROM user_companies WHERE role IS NOT NULL')
        print(f'\n   📋 Rôles actuels dans user_companies:')
        for role in current_roles:
            print(f'     - "{role["role"]}"')
        
        # 3. Mapping des anciennes valeurs vers les nouvelles
        role_mapping = {
            'admin': 'moa',        # Admin devient MOA par défaut
            'ADMIN': 'moa',
            'user': 'ent',         # User devient Entreprise par défaut
            'USER': 'ent',
            'manager': 'opc',      # Manager devient OPC
            'MANAGER': 'opc',
            # Ajouter d'autres mappings si nécessaire
        }
        
        print(f'\n   🔄 Conversion des rôles existants...')
        
        # 4. Convertir les valeurs existantes
        for old_role, new_role in role_mapping.items():
            result = await conn.execute(
                'UPDATE user_companies SET role = $1 WHERE role = $2',
                new_role, old_role
            )
            count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
            if count > 0:
                print(f'     ✅ {old_role} → {new_role} ({count} lignes)')
        
        # 5. Mettre les valeurs non mappées à 'ent' par défaut
        await conn.execute("""
            UPDATE user_companies 
            SET role = 'ent' 
            WHERE role NOT IN ('moa', 'moadel', 'archi', 'be', 'bc', 'opc', 'ent', 'fo')
            AND role IS NOT NULL
        """)
        
        # 6. Supprimer d'abord la valeur par défaut existante
        print(f'\n   🔧 Suppression de la valeur par défaut existante...')
        await conn.execute("""
            ALTER TABLE user_companies
            ALTER COLUMN role DROP DEFAULT
        """)
        print('   ✅ Valeur par défaut supprimée')

        # 7. Changer le type de colonne vers l'enum
        print(f'   🔧 Conversion de la colonne role vers companyrole...')
        await conn.execute("""
            ALTER TABLE user_companies
            ALTER COLUMN role TYPE companyrole
            USING role::companyrole
        """)
        print('   ✅ Colonne convertie vers companyrole')

        # 8. Définir une nouvelle valeur par défaut
        await conn.execute("""
            ALTER TABLE user_companies
            ALTER COLUMN role SET DEFAULT 'ent'::companyrole
        """)
        print('   ✅ Valeur par défaut définie: ent')
        
        # 9. Vérifier le résultat
        final_roles = await conn.fetch('SELECT DISTINCT role FROM user_companies ORDER BY role')
        print(f'\n   📋 Rôles finaux dans user_companies:')
        for role in final_roles:
            print(f'     - {role["role"]}')
        
        # 10. Vérifier la structure de la colonne
        column_info = await conn.fetchrow("""
            SELECT data_type, udt_name, column_default
            FROM information_schema.columns 
            WHERE table_name = 'user_companies' AND column_name = 'role'
        """)
        
        print(f'\n   📊 Structure finale de la colonne role:')
        print(f'     - Type: {column_info["data_type"]} ({column_info["udt_name"]})')
        print(f'     - Défaut: {column_info["column_default"]}')
        
        await conn.close()
        print('\n🎉 Enum companyrole créé et appliqué avec succès!')
        return True
        
    except Exception as e:
        print(f'\n❌ Erreur: {e}')
        await conn.close()
        return False

if __name__ == "__main__":
    success = asyncio.run(create_company_role_enum())
    if success:
        print('\n✅ user_companies utilise maintenant l\'enum companyrole!')
        print('   Rôles disponibles: moa, moadel, archi, be, bc, opc, ent, fo')
    else:
        print('\n❌ Échec de la création de l\'enum')
