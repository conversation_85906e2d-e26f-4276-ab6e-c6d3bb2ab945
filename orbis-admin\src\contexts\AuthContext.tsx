'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { FastAuthService, User } from '@/lib/fast-auth'

// Utiliser le type User du FastAuthService
type AuthUser = User

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  isAdmin: boolean
  isSuperAdmin: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Vérifier l'utilisateur connecté
    const checkAuth = () => {
      console.log('🔍 AuthContext - Vérification auth...')
      try {
        const isAuth = FastAuthService.isAuthenticated()
        console.log('🔍 AuthContext - isAuthenticated:', isAuth)

        if (isAuth) {
          const currentUser = FastAuthService.getUser()
          console.log('🔍 AuthContext - Utilisateur trouvé:', currentUser?.email)
          setUser(currentUser)
        } else {
          console.log('🔍 AuthContext - Aucun utilisateur connecté')
          setUser(null)
        }
      } catch (error) {
        console.error('❌ AuthContext - Erreur vérification auth:', error)
        FastAuthService.logout() // Nettoyer si erreur
        setUser(null)
      } finally {
        console.log('🔍 AuthContext - Fin du loading')
        setLoading(false)
      }
    }

    checkAuth()

    // Écouter les changements de localStorage pour détecter les connexions/déconnexions
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'orbis_jwt_token' || e.key === 'orbis_user') {
        console.log('🔍 AuthContext - Changement localStorage détecté:', e.key)
        checkAuth()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      console.log('🚀 AuthContext - Tentative de connexion pour:', email)
      await FastAuthService.login(email, password)

      // Attendre un peu pour que localStorage soit mis à jour
      await new Promise(resolve => setTimeout(resolve, 100))

      const currentUser = FastAuthService.getUser()
      console.log('✅ AuthContext - Connexion réussie, utilisateur:', currentUser?.email)
      console.log('✅ AuthContext - Token présent:', !!FastAuthService.getToken())
      console.log('✅ AuthContext - isAuthenticated:', FastAuthService.isAuthenticated())

      setUser(currentUser)
    } catch (error) {
      console.error('❌ AuthContext - Erreur de connexion:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      FastAuthService.logout()
      setUser(null)
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Vérifier si l'utilisateur est admin (MAJUSCULES)
  const isAdmin = user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN'

  // Vérifier si l'utilisateur est super admin (MAJUSCULES)
  const isSuperAdmin = user?.role === 'SUPER_ADMIN'

  const value = {
    user,
    loading,
    signIn,
    signOut,
    isAdmin,
    isSuperAdmin
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook pour protéger les routes
export function useRequireAuth() {
  const { user, loading } = useAuth()
  
  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/login'
    }
  }, [user, loading])

  return { user, loading }
}

// Hook pour les routes admin uniquement
export function useRequireAdmin() {
  const { user, loading, isAdmin } = useAuth()
  
  useEffect(() => {
    if (!loading) {
      if (!user) {
        window.location.href = '/login'
      } else if (!isAdmin) {
        window.location.href = '/unauthorized'
      }
    }
  }, [user, loading, isAdmin])

  return { user, loading, isAdmin }
}
