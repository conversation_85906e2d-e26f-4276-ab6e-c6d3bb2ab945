#!/usr/bin/env python3
"""
Script pour corriger les politiques RLS qui utilisent encore 'super_admin' en minuscule
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def fix_rls_policies_uppercase():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    print('🔧 Correction des politiques RLS pour utiliser SUPER_ADMIN...')
    
    try:
        # 1. Lister toutes les politiques existantes qui utilisent 'super_admin'
        policies = await conn.fetch("""
            SELECT schemaname, tablename, policyname, definition
            FROM pg_policies 
            WHERE definition LIKE '%super_admin%'
        """)
        
        print(f'\n📋 {len(policies)} politiques trouvées avec "super_admin":')
        for policy in policies:
            print(f'  - {policy["tablename"]}.{policy["policyname"]}')
        
        # 2. Supprimer toutes les politiques qui contiennent 'super_admin'
        print(f'\n🗑️ Suppression des anciennes politiques...')
        for policy in policies:
            try:
                await conn.execute(f'DROP POLICY IF EXISTS "{policy["policyname"]}" ON {policy["schemaname"]}.{policy["tablename"]}')
                print(f'  ✅ Supprimé: {policy["tablename"]}.{policy["policyname"]}')
            except Exception as e:
                print(f'  ⚠️ Erreur suppression {policy["policyname"]}: {e}')
        
        # 3. Recréer les politiques avec SUPER_ADMIN en majuscules
        print(f'\n🛡️ Création des nouvelles politiques avec SUPER_ADMIN...')
        
        tables = ['companies', 'users', 'user_companies', 'projects', 'time_entries', 'documents']
        
        for table in tables:
            try:
                # Politique super admin (accès total)
                await conn.execute(f"""
                    CREATE POLICY "Super admins have full access" ON public.{table}
                        FOR ALL USING (
                            EXISTS (
                                SELECT 1 FROM public.users 
                                WHERE users.supabase_user_id::text = auth.uid()::text 
                                AND users.role = 'SUPER_ADMIN'
                            )
                        )
                """)
                print(f'  ✅ Politique super admin créée pour {table}')
            except Exception as e:
                if 'already exists' in str(e):
                    print(f'  ⚠️ Politique super admin existe déjà pour {table}')
                else:
                    print(f'  ❌ Erreur création politique {table}: {e}')
        
        # 4. Vérifier les nouvelles politiques
        print(f'\n📊 Vérification des nouvelles politiques...')
        new_policies = await conn.fetch("""
            SELECT schemaname, tablename, policyname
            FROM pg_policies 
            WHERE definition LIKE '%SUPER_ADMIN%'
        """)
        
        print(f'  ✅ {len(new_policies)} politiques avec SUPER_ADMIN:')
        for policy in new_policies:
            print(f'    - {policy["tablename"]}.{policy["policyname"]}')
        
        # 5. Vérifier qu'il n'y a plus de 'super_admin' minuscule
        old_policies = await conn.fetch("""
            SELECT schemaname, tablename, policyname
            FROM pg_policies 
            WHERE definition LIKE '%super_admin%'
        """)
        
        if old_policies:
            print(f'\n⚠️ {len(old_policies)} politiques avec "super_admin" minuscule restantes:')
            for policy in old_policies:
                print(f'    - {policy["tablename"]}.{policy["policyname"]}')
        else:
            print(f'\n✅ Aucune politique avec "super_admin" minuscule restante')
        
        await conn.close()
        print('\n🎉 Correction des politiques RLS terminée!')
        return True
        
    except Exception as e:
        print(f'\n❌ Erreur: {e}')
        await conn.close()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_rls_policies_uppercase())
    if success:
        print('\n✅ Toutes les politiques RLS utilisent maintenant SUPER_ADMIN!')
    else:
        print('\n❌ Échec de la correction des politiques')
