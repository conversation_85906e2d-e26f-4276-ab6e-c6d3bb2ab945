#!/usr/bin/env python3
"""
Script pour standardiser les rôles dans user_companies en lowercase
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def fix_user_companies_roles():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    print('🔧 Standardisation des rôles user_companies en lowercase...')
    
    try:
        # 1. Voir l'état actuel
        current_roles = await conn.fetch('SELECT DISTINCT role FROM user_companies ORDER BY role')
        print('\n📋 Rôles actuels:')
        for role in current_roles:
            print(f'  - "{role["role"]}"')
        
        # 2. Mapping de standardisation
        role_mapping = {
            'ADMIN': 'admin',
            'SUPER_ADMIN': 'super_admin', 
            'CHEF_PROJET': 'chef_projet',
            'EMPLOYE': 'employe',
            'CLIENT': 'client',
            # Garder les lowercase tels quels
            'admin': 'admin',
            'super_admin': 'super_admin',
            'chef_projet': 'chef_projet',
            'employe': 'employe',
            'client': 'client'
        }
        
        print('\n🔄 Conversion des rôles...')
        
        # 3. Mettre à jour chaque rôle
        for old_role, new_role in role_mapping.items():
            if old_role != new_role:  # Seulement si changement nécessaire
                result = await conn.execute(
                    'UPDATE user_companies SET role = $1 WHERE role = $2',
                    new_role, old_role
                )
                count = int(result.split()[-1]) if result.split()[-1].isdigit() else 0
                if count > 0:
                    print(f'  ✅ {old_role} → {new_role} ({count} lignes)')
        
        # 4. Vérifier le résultat
        final_roles = await conn.fetch('SELECT DISTINCT role FROM user_companies ORDER BY role')
        print('\n📋 Rôles après standardisation:')
        for role in final_roles:
            print(f'  - "{role["role"]}"')
        
        # 5. Compter les utilisateurs par rôle
        role_counts = await conn.fetch('''
            SELECT role, COUNT(*) as count 
            FROM user_companies 
            GROUP BY role 
            ORDER BY role
        ''')
        
        print('\n📊 Répartition des rôles:')
        for row in role_counts:
            print(f'  - {row["role"]}: {row["count"]} utilisateurs')
        
        await conn.close()
        print('\n🎉 Standardisation terminée avec succès!')
        return True
        
    except Exception as e:
        print(f'\n❌ Erreur: {e}')
        await conn.close()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_user_companies_roles())
    if success:
        print('\n✅ Les rôles user_companies sont maintenant cohérents (lowercase)')
    else:
        print('\n❌ Échec de la standardisation')
