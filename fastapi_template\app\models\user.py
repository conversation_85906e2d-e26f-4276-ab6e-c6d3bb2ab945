# app/models/user.py
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid
from app.core.database import Base

class UserRole(str, enum.Enum):
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    CHEF_PROJET = "chef_projet"
    EMPLOYE = "employe"
    CLIENT = "client"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)  # Clé primaire locale auto-incrémentée
    supabase_user_id = Column(String, unique=True, index=True, nullable=True)  # UUID Supabase pour liaison auth
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=True)  # Optionnel car géré par Supabase
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)

    role = Column(String, default="employe")
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Additional security fields
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    
    # Profile fields
    phone = Column(String, nullable=True)
    avatar_url = Column(String, nullable=True)
    notes = Column(Text, nullable=True)

    # Relationships
    companies = relationship("UserCompany", back_populates="user", foreign_keys="UserCompany.user_id")
    time_entries = relationship("TimeEntry", back_populates="user")
    employee_assignments = relationship("EmployeeAssignment", back_populates="user")
    
    @property
    def full_name(self) -> str:
        """Return the full name computed from first and last name"""
        return f"{self.first_name} {self.last_name}" if self.first_name and self.last_name else ""
    
    @property
    def display_name(self) -> str:
        """Return the full name or email as display name"""
        return self.full_name or self.email
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission based on role"""
        role_permissions = {
            UserRole.SUPER_ADMIN: ['*'],  # All permissions
            UserRole.ADMIN: [
                'manage_users', 'manage_projects', 'manage_companies',
                'view_all_projects', 'view_all_users', 'manage_documents'
            ],
            UserRole.CHEF_PROJET: [
                'create_projects', 'manage_assigned_projects', 'view_team_projects',
                'manage_project_documents', 'view_project_stats'
            ],
            UserRole.EMPLOYE: [
                'view_assigned_projects', 'create_time_entries', 'view_own_stats',
                'upload_documents'
            ],
            UserRole.CLIENT: [
                'view_own_projects', 'view_project_progress', 'download_documents'
            ]
        }
        
        user_permissions = role_permissions.get(self.role, [])
        return '*' in user_permissions or permission in user_permissions
    
    def can_access_project(self, project_id: int) -> bool:
        """Check if user can access specific project"""
        if self.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN]:
            return True
        # Additional logic for project-specific access can be added here
        return False
    
    def is_account_locked(self) -> bool:
        """Check if account is temporarily locked"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False