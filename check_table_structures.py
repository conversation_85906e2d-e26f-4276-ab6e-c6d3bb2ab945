#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_table_structures():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    # Vérifier la structure des tables users et user_companies
    tables = ['users', 'user_companies']
    
    for table in tables:
        print(f'\n🔍 Structure de la table {table}:')
        columns = await conn.fetch(f"""
            SELECT 
                column_name, 
                data_type, 
                udt_name,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = '{table}' 
            ORDER BY ordinal_position
        """)
        
        for col in columns:
            nullable = "NULL" if col["is_nullable"] == "YES" else "NOT NULL"
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f'  - {col["column_name"]}: {col["data_type"]} ({col["udt_name"]}) {nullable}{default}')
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_table_structures())
