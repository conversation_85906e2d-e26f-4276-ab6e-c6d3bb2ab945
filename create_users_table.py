#!/usr/bin/env python3
"""
Script pour créer la table users applicative avec la bonne structure
"""

import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def create_users_table():
    """Créer la table users applicative"""
    
    # Configuration de la base de données
    DATABASE_URL = os.getenv("DATABASE_URL")
    if not DATABASE_URL:
        print("❌ DATABASE_URL non trouvé dans .env")
        return False
    
    print("🔧 Création de la table users applicative")
    print(f"📍 Base de données: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'localhost'}")
    
    try:
        # Connexion à la base de données (avec statement_cache_size=0 pour pgbouncer)
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        print("✅ Connexion à la base de données réussie")
        
        # 1. Supprimer la table existante si elle existe
        print("\n1. 🗑️ Suppression de l'ancienne table...")
        await conn.execute("DROP TABLE IF EXISTS public.users CASCADE")
        print("  ✅ Ancienne table supprimée")
        
        # 2. Recréer l'enum UserRole avec les bonnes valeurs
        print("\n2. 📝 Recréation de l'enum UserRole...")
        try:
            # Supprimer l'ancien enum s'il existe
            await conn.execute("DROP TYPE IF EXISTS userrole CASCADE")
            print("  ✅ Ancien enum supprimé")

            # Créer le nouvel enum
            await conn.execute("""
                CREATE TYPE userrole AS ENUM (
                    'super_admin',
                    'admin',
                    'chef_projet',
                    'employe',
                    'client'
                )
            """)
            print("  ✅ Enum UserRole créé avec les bonnes valeurs")
        except Exception as e:
            print(f"  ⚠️ Erreur création enum: {e}")
            raise
        
        # 3. Créer la table users avec la bonne structure
        print("\n3. 🏗️ Création de la table users...")
        await conn.execute("""
            CREATE TABLE public.users (
                id SERIAL PRIMARY KEY,
                supabase_user_id VARCHAR UNIQUE,
                email VARCHAR UNIQUE NOT NULL,
                hashed_password VARCHAR,
                first_name VARCHAR NOT NULL,
                last_name VARCHAR NOT NULL,
                role userrole DEFAULT 'employe',
                is_active BOOLEAN DEFAULT true,
                is_superuser BOOLEAN DEFAULT false,
                is_verified BOOLEAN DEFAULT false,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
                last_login TIMESTAMP WITHOUT TIME ZONE,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP WITHOUT TIME ZONE,
                phone VARCHAR,
                avatar_url VARCHAR,
                notes TEXT
            )
        """)
        print("  ✅ Table users créée avec id INTEGER auto-incrémenté")
        
        # 4. Créer des index
        print("\n4. 📊 Création des index...")
        await conn.execute("CREATE INDEX idx_users_email ON public.users(email)")
        await conn.execute("CREATE INDEX idx_users_supabase_id ON public.users(supabase_user_id)")
        await conn.execute("CREATE INDEX idx_users_active ON public.users(is_active)")
        print("  ✅ Index créés")
        
        # 5. Insérer l'utilisateur super admin de test
        print("\n5. 👤 Création de l'utilisateur super admin...")
        admin_id = await conn.fetchval("""
            INSERT INTO public.users (
                email, first_name, last_name, role, is_active, is_superuser, is_verified
            ) VALUES (
                '<EMAIL>', 'Jeremy', 'Giaime', 'super_admin', true, true, true
            ) RETURNING id
        """)
        print(f"  ✅ Super admin créé avec ID: {admin_id}")
        
        # 6. Vérification finale
        print("\n6. ✅ Vérification finale...")
        
        # Vérifier la structure
        structure = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'users'
            ORDER BY ordinal_position;
        """)
        
        print("  Structure de la table users:")
        for row in structure:
            default = row['column_default'][:30] + '...' if row['column_default'] and len(row['column_default']) > 30 else row['column_default']
            print(f"    - {row['column_name']}: {row['data_type']} ({'NULL' if row['is_nullable'] == 'YES' else 'NOT NULL'}) {default or ''}")
        
        # Vérifier les données
        user_count = await conn.fetchval("SELECT COUNT(*) FROM public.users")
        print(f"  ✅ {user_count} utilisateur(s) dans la table")
        
        await conn.close()
        print("\n🎉 Table users créée avec succès !")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la création: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Script de création de la table users")
    print("⚠️  ATTENTION: Ce script va recréer la table users")
    
    confirm = input("\nContinuer ? (oui/non): ").lower().strip()
    if confirm in ['oui', 'o', 'yes', 'y']:
        success = asyncio.run(create_users_table())
        if success:
            print("\n✅ Table créée ! Vous pouvez maintenant redémarrer le serveur.")
        else:
            print("\n❌ Création échouée. Vérifiez les erreurs ci-dessus.")
    else:
        print("❌ Création annulée.")
