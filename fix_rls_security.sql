-- Script pour corriger les erreurs RLS (Row Level Security) dans Supabase
-- Ce script active RLS sur toutes les tables publiques et crée des politiques de base

-- =====================================================
-- 1. ACTIVER RLS SUR TOUTES LES TABLES
-- =====================================================

-- Tables principales
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_lines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 2. CRÉER DES POLITIQUES RLS DE BASE
-- =====================================================

-- Politique pour les super admins (accès total)
CREATE POLICY "Super admins have full access" ON public.companies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Super admins have full access" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Super admins have full access" ON public.user_companies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

-- Politique pour les utilisateurs normaux (accès à leur entreprise uniquement)
CREATE POLICY "Users can access their company data" ON public.companies
    FOR SELECT USING (
        id IN (
            SELECT company_id FROM public.user_companies uc
            JOIN public.users u ON u.id = uc.user_id
            WHERE u.id = auth.uid()::text::integer
        )
    );

CREATE POLICY "Users can access their company users" ON public.users
    FOR SELECT USING (
        id IN (
            SELECT u.id FROM public.users u
            JOIN public.user_companies uc ON u.id = uc.user_id
            JOIN public.user_companies my_uc ON uc.company_id = my_uc.company_id
            JOIN public.users me ON me.id = my_uc.user_id
            WHERE me.id = auth.uid()::text::integer
        )
    );

-- Politiques pour les projets (accès basé sur l'entreprise)
CREATE POLICY "Super admins have full access" ON public.projects
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their company projects" ON public.projects
    FOR SELECT USING (
        company_id IN (
            SELECT company_id FROM public.user_companies uc
            JOIN public.users u ON u.id = uc.user_id
            WHERE u.id = auth.uid()::text::integer
        )
    );

-- Politiques pour les tâches
CREATE POLICY "Super admins have full access" ON public.tasks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their company tasks" ON public.tasks
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM public.projects p
            JOIN public.user_companies uc ON p.company_id = uc.company_id
            JOIN public.users u ON u.id = uc.user_id
            WHERE u.id = auth.uid()::text::integer
        )
    );

-- Politiques pour les matériaux et catégories (accès global en lecture)
CREATE POLICY "Super admins have full access" ON public.materials
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Authenticated users can read materials" ON public.materials
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Super admins have full access" ON public.material_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Authenticated users can read material categories" ON public.material_categories
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- Politiques pour les lignes de budget
CREATE POLICY "Super admins have full access" ON public.budget_lines
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their company budget lines" ON public.budget_lines
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM public.projects p
            JOIN public.user_companies uc ON p.company_id = uc.company_id
            JOIN public.users u ON u.id = uc.user_id
            WHERE u.id = auth.uid()::text::integer
        )
    );

-- Politiques pour les entrées de temps
CREATE POLICY "Super admins have full access" ON public.time_entries
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their time entries" ON public.time_entries
    FOR ALL USING (
        user_id = auth.uid()::text::integer
        OR EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role IN ('super_admin', 'admin', 'chef_projet')
        )
    );

-- Politiques pour les documents
CREATE POLICY "Super admins have full access" ON public.documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their company documents" ON public.documents
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM public.projects p
            JOIN public.user_companies uc ON p.company_id = uc.company_id
            JOIN public.users u ON u.id = uc.user_id
            WHERE u.id = auth.uid()::text::integer
        )
    );

-- Politiques pour les notifications
CREATE POLICY "Super admins have full access" ON public.notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid()::text::integer 
            AND users.role = 'super_admin'
        )
    );

CREATE POLICY "Users can access their notifications" ON public.notifications
    FOR ALL USING (user_id = auth.uid()::text::integer);

-- =====================================================
-- 3. VÉRIFICATION
-- =====================================================

-- Vérifier que RLS est activé sur toutes les tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
