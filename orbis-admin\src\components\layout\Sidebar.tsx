'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Building2, 
  Users, 
  BarChart3, 
  Shield, 
  Settings, 
  Home,
  ChevronLeft,
  ChevronRight,
  ExternalLink
} from 'lucide-react'

const navigationItems = [
  {
    name: 'Tableau de bord',
    href: '/',
    icon: Home
  },
  {
    name: 'Entreprises',
    href: '/companies',
    icon: Building2
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: BarChart3
  },
  {
    name: 'Sécurité',
    href: '/security',
    icon: Shield
  },
  {
    name: 'Paramètres',
    href: '/settings',
    icon: Settings
  }
]

interface SidebarProps {
  user?: {
    email: string
    name?: string
  }
}

export default function Sidebar({ user }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()

  return (
    <>
      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 z-40 h-screen bg-white border-r border-gray-200 shadow-lg
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            {!isCollapsed && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">ORBIS</h1>
                  <p className="text-xs text-green-600 font-medium">ADMIN PORTAL</p>
                </div>
              </div>
            )}
            
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
              title={isCollapsed ? 'Étendre' : 'Réduire'}
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronLeft className="w-4 h-4 text-gray-600" />
              )}
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/admin' && pathname.startsWith(item.href))
              const IconComponent = item.icon

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                  title={isCollapsed ? item.name : undefined}
                >
                  <div className={`flex-shrink-0 ${isActive ? 'text-green-600' : 'text-gray-500'}`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  {!isCollapsed && (
                    <>
                      <span className="ml-3 truncate">{item.name}</span>
                      {item.badge && (
                        <span className="ml-auto badge-success">
                          {item.badge}
                        </span>
                      )}
                    </>
                  )}
                  {isActive && !isCollapsed && (
                    <span className="absolute right-3 w-1.5 h-1.5 rounded-full bg-green-600"></span>
                  )}
                </Link>
              )
            })}
          </nav>

          {/* User Section */}
          <div className="border-t border-gray-200 p-3">
            {!isCollapsed && (
              <div className="space-y-3">
                {/* User Info */}
                <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50">
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user?.email?.charAt(0).toUpperCase() || 'A'}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user?.name || 'Admin'}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {user?.email}
                    </p>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="space-y-1">
                  <Link
                    href={process.env.NEXT_PUBLIC_CUSTOMER_APP_URL || 'http://localhost:3000'}
                    className="flex items-center space-x-2 text-xs text-green-600 hover:text-green-700 p-2 rounded-lg hover:bg-green-50 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>App Client</span>
                  </Link>
                </div>
              </div>
            )}
            
            {isCollapsed && (
              <div className="flex justify-center">
                <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.email?.charAt(0).toUpperCase() || 'A'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Spacer for fixed sidebar */}
      <div className={`${isCollapsed ? 'w-16' : 'w-64'} flex-shrink-0 transition-all duration-300`} />
    </>
  )
}
