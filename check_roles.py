#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_roles():
    load_dotenv()
    conn = await asyncpg.connect(os.getenv('DATABASE_URL'), statement_cache_size=0)
    
    # Vérifier les valeurs d'enum autorisées
    enum_values = await conn.fetch("SELECT unnest(enum_range(NULL::userrole)) as role_value")
    print('🔍 Valeurs enum autorisées:')
    for row in enum_values:
        print(f'  - {row["role_value"]}')
    
    # Vérifier les rôles actuels dans la table users
    users = await conn.fetch('SELECT email, role FROM users')
    print('\n🔍 Rôles actuels dans la table users:')
    for user in users:
        print(f'  - {user["email"]}: {user["role"]}')
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_roles())
